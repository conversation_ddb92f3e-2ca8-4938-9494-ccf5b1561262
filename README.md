# Cliente WebSocket Node.js

Este projeto contém um cliente WebSocket em Node.js que se conecta a um servidor WebSocket e faz console.log de todos os eventos recebidos.

## Funcionalidades

- ✅ Conexão automática ao WebSocket
- 📨 Log detalhado de todas as mensagens recebidas
- 🔄 Reconexão automática em caso de desconexão
- 🔍 Parse automático de mensagens JSON
- ❌ Tratamento de erros
- 🛑 Graceful shutdown (Ctrl+C)

## Instalação

1. Instale as dependências:
```bash
npm install
```

## Uso

1. **Edite a URL do WebSocket** no arquivo `websocket-client.js`:
```javascript
const WEBSOCKET_URL = 'ws://localhost:8080'; // Altere para sua URL
```

2. **Execute o cliente**:
```bash
npm start
```

ou

```bash
node websocket-client.js
```

## Exemplo <PERSON>

```
🚀 Iniciando cliente WebSocket...
📋 Configurações:
   URL: ws://localhost:8080
   Reconexão automática: Sim (máx. 10 tentativas)
──────────────────────────────────────────────────
🔌 Tentando conectar ao WebSocket: ws://localhost:8080
✅ Conexão WebSocket estabelecida com sucesso!
📡 Conectado em: 2024-01-15T10:30:45.123Z

📨 MENSAGEM RECEBIDA:
⏰ Timestamp: 2024-01-15T10:30:46.456Z
📄 Dados brutos: {"type":"welcome","message":"Bem-vindo!"}
🔍 Dados parseados (JSON):
{
  "type": "welcome",
  "message": "Bem-vindo!"
}
──────────────────────────────────────────────────
```

## Personalização

### Alterar URL do WebSocket
Edite a constante `WEBSOCKET_URL` no início do arquivo:
```javascript
const WEBSOCKET_URL = 'wss://seu-servidor.com/websocket';
```

### Enviar Mensagens
Descomente e modifique o exemplo no final do arquivo:
```javascript
setTimeout(() => {
    client.sendMessage(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
}, 5000);
```

### Configurar Reconexão
Modifique as propriedades da classe:
```javascript
this.reconnectInterval = 5000; // 5 segundos
this.maxReconnectAttempts = 10; // máximo de tentativas
```

## Parar o Cliente

Pressione `Ctrl+C` para parar o cliente de forma segura.
