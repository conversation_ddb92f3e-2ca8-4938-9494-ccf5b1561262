const WebSocket = require('ws');

// Configuração do WebSocket
const WEBSOCKET_URL = 'ws://34.75.222.36'; // Altere para a URL do seu WebSocket

class WebSocketClient {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.reconnectInterval = 5000; // 5 segundos
        this.maxReconnectAttempts = 10;
        this.reconnectAttempts = 0;
    }

    connect() {
        try {
            console.log(`🔌 Tentando conectar ao WebSocket: ${this.url}`);
            this.ws = new WebSocket(this.url);

            // Evento: Conexão estabelecida
            this.ws.on('open', () => {
                console.log('✅ Conexão WebSocket estabelecida com sucesso!');
                console.log(`📡 Conectado em: ${new Date().toISOString()}`);
                this.reconnectAttempts = 0;
            });

            // Evento: Mensagem recebida
            this.ws.on('message', (data) => {
                console.log('\n📨 MENSAGEM RECEBIDA:');
                console.log('⏰ Timestamp:', new Date().toISOString());
                console.log('📄 Dados brutos:', data.toString());
                
                // Tenta fazer parse do JSON se possível
                try {
                    const jsonData = JSON.parse(data.toString());
                    console.log('🔍 Dados parseados (JSON):');
                    console.log(JSON.stringify(jsonData, null, 2));
                } catch (e) {
                    console.log('📝 Dados como texto:', data.toString());
                }
                console.log('─'.repeat(50));
            });

            // Evento: Erro
            this.ws.on('error', (error) => {
                console.error('❌ ERRO no WebSocket:');
                console.error('⏰ Timestamp:', new Date().toISOString());
                console.error('🔥 Erro:', error.message);
                console.error('─'.repeat(50));
            });

            // Evento: Conexão fechada
            this.ws.on('close', (code, reason) => {
                console.log('\n🔌 CONEXÃO FECHADA:');
                console.log('⏰ Timestamp:', new Date().toISOString());
                console.log('🔢 Código:', code);
                console.log('📝 Motivo:', reason.toString() || 'Não especificado');
                console.log('─'.repeat(50));
                
                this.handleReconnect();
            });

        } catch (error) {
            console.error('❌ Erro ao tentar conectar:', error.message);
            this.handleReconnect();
        }
    }

    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 Tentativa de reconexão ${this.reconnectAttempts}/${this.maxReconnectAttempts} em ${this.reconnectInterval/1000}s...`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectInterval);
        } else {
            console.log('❌ Máximo de tentativas de reconexão atingido. Parando...');
        }
    }

    // Método para enviar mensagens (opcional)
    sendMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('\n📤 ENVIANDO MENSAGEM:');
            console.log('⏰ Timestamp:', new Date().toISOString());
            console.log('📄 Mensagem:', message);
            this.ws.send(message);
            console.log('─'.repeat(50));
        } else {
            console.log('❌ WebSocket não está conectado. Não é possível enviar mensagem.');
        }
    }

    // Método para fechar conexão
    disconnect() {
        if (this.ws) {
            console.log('🔌 Fechando conexão WebSocket...');
            this.ws.close();
        }
    }
}

// Função principal
function main() {
    console.log('🚀 Iniciando cliente WebSocket...');
    console.log('📋 Configurações:');
    console.log(`   URL: ${WEBSOCKET_URL}`);
    console.log(`   Reconexão automática: Sim (máx. 10 tentativas)`);
    console.log('─'.repeat(50));

    const client = new WebSocketClient(WEBSOCKET_URL);
    client.connect();

    // Exemplo de como enviar uma mensagem após 5 segundos (opcional)
    // setTimeout(() => {
    //     client.sendMessage(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
    // }, 5000);

    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n\n🛑 Recebido sinal de interrupção (Ctrl+C)');
        client.disconnect();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n\n🛑 Recebido sinal de término');
        client.disconnect();
        process.exit(0);
    });
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
    main();
}

module.exports = WebSocketClient;
